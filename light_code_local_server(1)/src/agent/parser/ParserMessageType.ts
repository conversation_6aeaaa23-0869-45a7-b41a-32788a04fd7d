export enum ParserMessageType {
    // Represents a plain text message type
    txt = 'txt',
    // Represents a message type for reasoning text
    reasoningTxt = 'reasoningTxt',
    // Represents a message type for tool call initiation
    toolCall = 'toolCall',
    // Represents a message type for continued call
    continueCall = 'continueCall',
    // Represents a message type indicating stop
    stop = 'stop'
}